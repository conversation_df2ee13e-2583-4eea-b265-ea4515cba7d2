using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DateFactoryApp.Models;
using DateFactoryApp.Services;

namespace DateFactoryApp.ViewModels
{
    public partial class CustomersViewModel : ObservableObject
    {
        private readonly ICustomerService _customerService;

        [ObservableProperty]
        private ObservableCollection<Customer> customers = new();

        [ObservableProperty]
        private Customer? selectedCustomer;

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private bool isEditMode = false;

        [ObservableProperty]
        private Customer editingCustomer = new();

        public CustomersViewModel(ICustomerService customerService)
        {
            _customerService = customerService;

            // Initialize with empty collections
            Customers = new ObservableCollection<Customer>();

            // Load data safely
            LoadDataSafely();
        }

        private async void LoadDataSafely()
        {
            try
            {
                await LoadDataAsync();
            }
            catch (Exception ex)
            {
                // Log error but don't crash
                System.Diagnostics.Debug.WriteLine($"Error loading customers data: {ex.Message}");
            }
        }

        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;

                var customersData = await _customerService.GetAllCustomersAsync();

                Customers.Clear();
                foreach (var customer in customersData)
                {
                    Customers.Add(customer);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void AddCustomer()
        {
            EditingCustomer = new Customer
            {
                IsActive = true,
                Type = CustomerType.Retail
            };
            IsEditMode = true;
        }

        [RelayCommand]
        private void EditCustomer()
        {
            if (SelectedCustomer == null) return;

            EditingCustomer = new Customer
            {
                Id = SelectedCustomer.Id,
                Name = SelectedCustomer.Name,
                Code = SelectedCustomer.Code,
                Phone = SelectedCustomer.Phone,
                Email = SelectedCustomer.Email,
                Address = SelectedCustomer.Address,
                City = SelectedCustomer.City,
                Region = SelectedCustomer.Region,
                Type = SelectedCustomer.Type,
                CreditLimit = SelectedCustomer.CreditLimit,
                IsActive = SelectedCustomer.IsActive,
                Notes = SelectedCustomer.Notes
            };
            IsEditMode = true;
        }

        private async Task SaveCustomerAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(EditingCustomer.Name))
                {
                    MessageBox.Show("يرجى إدخال اسم العميل", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                IsLoading = true;

                if (EditingCustomer.Id == 0)
                {
                    await _customerService.CreateCustomerAsync(EditingCustomer);
                }
                else
                {
                    await _customerService.UpdateCustomerAsync(EditingCustomer);
                }

                IsEditMode = false;
                await LoadDataAsync();

                MessageBox.Show("تم حفظ العميل بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ العميل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void CancelEdit()
        {
            IsEditMode = false;
            EditingCustomer = new Customer();
        }

        private async Task DeleteCustomerAsync()
        {
            if (SelectedCustomer == null) return;

            var result = MessageBox.Show($"هل تريد حذف العميل '{SelectedCustomer.Name}'؟",
                "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    IsLoading = true;
                    await _customerService.DeleteCustomerAsync(SelectedCustomer.Id);
                    await LoadDataAsync();

                    MessageBox.Show("تم حذف العميل بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف العميل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    IsLoading = false;
                }
            }
        }

        private async Task SearchAsync()
        {
            try
            {
                IsLoading = true;

                var allCustomers = await _customerService.GetAllCustomersAsync();

                if (string.IsNullOrWhiteSpace(SearchText))
                {
                    Customers.Clear();
                    foreach (var customer in allCustomers)
                    {
                        Customers.Add(customer);
                    }
                }
                else
                {
                    var filteredCustomers = allCustomers.Where(c =>
                        c.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                        (c.Code?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                        (c.Phone?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ?? false));

                    Customers.Clear();
                    foreach (var customer in filteredCustomers)
                    {
                        Customers.Add(customer);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task ShowCustomersWithBalanceAsync()
        {
            try
            {
                IsLoading = true;

                var customersWithBalance = await _customerService.GetCustomersWithBalanceAsync();

                Customers.Clear();
                foreach (var customer in customersWithBalance)
                {
                    Customers.Add(customer);
                }

                if (!customersWithBalance.Any())
                {
                    MessageBox.Show("لا توجد عملاء برصيد", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task ViewCustomerSalesAsync()
        {
            if (SelectedCustomer == null) return;

            try
            {
                IsLoading = true;
                var sales = await _customerService.GetCustomerSalesAsync(SelectedCustomer.Id);

                if (sales.Any())
                {
                    var message = $"مبيعات العميل '{SelectedCustomer.Name}':\n\n";
                    var totalAmount = 0m;

                    foreach (var sale in sales.Take(10))
                    {
                        message += $"• فاتورة {sale.InvoiceNumber} - {sale.SaleDate:yyyy/MM/dd} - {sale.TotalAmount:C}\n";
                        totalAmount += sale.TotalAmount;
                    }

                    if (sales.Count() > 10)
                    {
                        message += $"\n... و {sales.Count() - 10} فاتورة أخرى";
                    }

                    message += $"\n\nإجمالي المبيعات: {totalAmount:C}";

                    MessageBox.Show(message, "مبيعات العميل", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("لا توجد مبيعات لهذا العميل", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل مبيعات العميل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void ExportCustomers()
        {
            // TODO: Implement export functionality
            MessageBox.Show("تصدير العملاء سيكون متاح قريباً", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        partial void OnSearchTextChanged(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                _ = SearchAsync();
            }
        }
    }
}
