using System;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using DateFactoryApp.Data;
using DateFactoryApp.Services;
using DateFactoryApp.ViewModels;
using DateFactoryApp.Views;
using Serilog;

namespace DateFactoryApp
{
    public partial class App : Application
    {
        private IServiceProvider? _serviceProvider;

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Configure Serilog
            Log.Logger = new LoggerConfiguration()
                .WriteTo.File("logs/app-.txt", rollingInterval: RollingInterval.Day)
                .CreateLogger();

            try
            {
                // Configure dependency injection
                var services = new ServiceCollection();
                ConfigureServices(services);
                _serviceProvider = services.BuildServiceProvider();

                // Show login window directly
                var loginWindow = new LoginWindow();
                loginWindow.Show();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Application failed to start");
                MessageBox.Show($"فشل في بدء التطبيق: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        private void ConfigureServices(IServiceCollection services)
        {
            // Database
            services.AddDbContext<AppDbContext>(options =>
                options.UseSqlite("Data Source=DateFactory.db"));

            // Services
            services.AddSingleton<IUserService, UserService>();
            services.AddSingleton<IProductService, ProductService>();
            services.AddSingleton<ISalesService, SalesService>();
            services.AddSingleton<IInventoryService, InventoryService>();
            services.AddSingleton<ICustomerService, CustomerService>();
            services.AddSingleton<ISupplierService, SupplierService>();
            services.AddSingleton<IReportService, ReportService>();
            services.AddSingleton<IBackupService, BackupService>();
            services.AddSingleton<ISyncService, SyncService>();
            services.AddSingleton<IThemeService, ThemeService>();

            // ViewModels
            services.AddTransient<LoginViewModel>();
            services.AddTransient<MainViewModel>();
            services.AddTransient<DashboardViewModel>();
            services.AddTransient<ProductsViewModel>();
            services.AddTransient<SalesViewModel>();
            services.AddTransient<InventoryViewModel>();
            services.AddTransient<CustomersViewModel>();
            services.AddTransient<SuppliersViewModel>();
            services.AddTransient<ReportsViewModel>();
            services.AddTransient<SettingsViewModel>();
            services.AddTransient<ProductionViewModel>();
        }

        public static T GetService<T>() where T : class
        {
            var app = Current as App;
            if (app?._serviceProvider == null)
                throw new InvalidOperationException("Service provider not initialized");

            return app._serviceProvider.GetRequiredService<T>();
        }

        public static object GetService(Type serviceType)
        {
            var app = Current as App;
            if (app?._serviceProvider == null)
                throw new InvalidOperationException("Service provider not initialized");

            return app._serviceProvider.GetRequiredService(serviceType);
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _serviceProvider?.GetService<IServiceScope>()?.Dispose();
            Log.CloseAndFlush();
            base.OnExit(e);
        }
    }
}
